# expo create project
npx create-expo-app@latest

npx @react-native-community/cli codegen --platform ios --outputPath ios/

npx @react-native-community/cli codegen --platform android

# generate ios
node node_modules/react-native/scripts/generate-codegen-artifacts.js \
    --path . \
    --outputPath ios/ \
    --targetPlatform ios


# npm install -g expo-cli
npx expo 
npx react-native start
npx expo-doctor
npx expo prebuild --clean
npx expo prebuild
# npx pod-install
npx expo run:ios --configuration Release

npx expo run:android --variant release


npx react-native build-android --mode=release


npx pod-install

npx expo start --web

# Android apps
npx react-native run-android

# iOS apps
cd ios/
pod install --repo-update
cd ..
npx react-native run-ios


#* install firebase for expo
npx expo install @react-native-firebase/app

npx expo install @react-native-firebase/auth @react-native-firebase/crashlytics @react-native-firebase/messaging @react-native-firebase/remote-config @react-native-firebase/perf @react-native-firebase/firestore @react-native-firebase/storage @react-native-firebase/in-app-messaging @react-native-firebase/analytics


# macos 



# packages

#npx react-native upgrade
# removed:
#    "@testing-library/react-hooks": "^8.0.1",
#    "@testing-library/react-native": "^12.9.0",


react-native link react-native-device-info



npm install @react-native-community/cli-platform-ios --save
npx react-native-macos-init
pod install --project-directory=macos
    • npx react-native run-macos
    • yarn start:macos

npx react-native run-macos


# eas

eas build --platform all

# clean build

npx expo start -c


# export web
 npx expo export -p web

 firebase deploy